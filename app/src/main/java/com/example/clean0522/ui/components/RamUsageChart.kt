package com.example.clean0522.ui.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.clean0522.R
import com.example.clean0522.utils.RamUsageUtils

/**
 * RAM usage chart composable that displays a real-time curve with filled area
 */
@Composable
fun RamUsageChart(
    dataPoints: List<RamUsageUtils.ChartDataPoint>,
    minMemoryBytes: Long,
    maxMemoryBytes: Long,
    currentRamInfo: RamUsageUtils.RamUsageInfo?,
    modifier: Modifier = Modifier
) {
    val chartColor = Color(0xFF8A5CF6) // Purple color to match the design
    val backgroundColor = Color(0xFFFFF5FD) // Light gray background

    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(200.dp)
            .background(backgroundColor, RoundedCornerShape(12.dp))
            .border(1.5.dp,chartColor,RoundedCornerShape(12.dp))
            .padding(16.dp)
    ) {
        // 创建显示用的数据点
        val displayDataPoints = if (dataPoints.isEmpty() && currentRamInfo != null) {
            // 如果没有数据，用当前RAM的一半初始化10个数据点
            val halfRam = currentRamInfo.totalMemoryBytes / 2
            List(10) { index ->
                RamUsageUtils.ChartDataPoint(
                    usedMemoryBytes = halfRam,
                    timestamp = System.currentTimeMillis() - (9 - index) * 1000L
                )
            }
        } else {
            dataPoints
        }

        Canvas(
            modifier = Modifier.fillMaxSize()
        ) {
            drawProgressiveRamChart(
                dataPoints = displayDataPoints,
                minMemoryBytes = minMemoryBytes,
                maxMemoryBytes = maxMemoryBytes,
                chartColor = chartColor,
                canvasSize = size
            )
        }
    }
}

/**
 * Draw the progressive RAM usage chart - shows data points from left to right
 */
private fun DrawScope.drawProgressiveRamChart(
    dataPoints: List<RamUsageUtils.ChartDataPoint>,
    minMemoryBytes: Long,
    maxMemoryBytes: Long,
    chartColor: Color,
    canvasSize: androidx.compose.ui.geometry.Size
) {
    val width = canvasSize.width
    val height = canvasSize.height
    val padding = 20f

    // Calculate drawing area
    val drawingWidth = width - (padding * 2)
    val drawingHeight = height - (padding * 2)

    if (dataPoints.isEmpty()) return

    val maxPoints = 10
    val stepX = drawingWidth / (maxPoints - 1)

    // Create paths for the curve and fill
    val curvePath = Path()
    val fillPath = Path()
    val points = mutableListOf<Offset>()

    dataPoints.forEachIndexed { index, dataPoint ->
        val x = padding + (index * stepX)

        val normalizedValue = if (maxMemoryBytes > minMemoryBytes) {
            (dataPoint.usedMemoryBytes - minMemoryBytes).toFloat() / (maxMemoryBytes - minMemoryBytes).toFloat()
        } else {
            0.5f
        }

        val y = padding + drawingHeight - (normalizedValue * drawingHeight)
        points.add(Offset(x, y))
    }

    if (points.isNotEmpty()) {
        when (points.size) {
            1 -> {
                drawCircle(
                    color = chartColor,
                    radius = 4.dp.toPx(),
                    center = points[0]
                )
            }
            else -> {
                fillPath.moveTo(points[0].x, height - padding) // 从底部开始
                fillPath.lineTo(points[0].x, points[0].y) // 到第一个点

                curvePath.moveTo(points[0].x, points[0].y)

                for (i in 1 until points.size) {
                    val currentPoint = points[i]
                    val previousPoint = points[i - 1]

                    if (points.size == 2) {
                        curvePath.lineTo(currentPoint.x, currentPoint.y)
                        fillPath.lineTo(currentPoint.x, currentPoint.y)
                    } else {
                        val controlPoint1X = previousPoint.x + (currentPoint.x - previousPoint.x) * 0.3f
                        val controlPoint1Y = previousPoint.y
                        val controlPoint2X = currentPoint.x - (currentPoint.x - previousPoint.x) * 0.3f
                        val controlPoint2Y = currentPoint.y

                        curvePath.cubicTo(
                            controlPoint1X, controlPoint1Y,
                            controlPoint2X, controlPoint2Y,
                            currentPoint.x, currentPoint.y
                        )
                        fillPath.cubicTo(
                            controlPoint1X, controlPoint1Y,
                            controlPoint2X, controlPoint2Y,
                            currentPoint.x, currentPoint.y
                        )
                    }
                }

                fillPath.lineTo(points.last().x, height - padding) // 到底部
                fillPath.close()

                val gradient = Brush.verticalGradient(
                    colors = listOf(
                        Color(0x94FF80BB), // #FF80BB with 58% opacity
                        Color(0x00FF70B2)  // #FF70B2 with 0% opacity
                    ),
                    startY = padding,
                    endY = height - padding
                )

                drawPath(
                    path = fillPath,
                    brush = gradient
                )

                drawPath(
                    path = curvePath,
                    color = chartColor,
                    style = Stroke(
                        width = 3.dp.toPx(),
                        cap = StrokeCap.Round
                    )
                )
            }
        }
    }
}