package com.example.clean0522.ui.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.clean0522.R
import com.example.clean0522.utils.RamUsageUtils

/**
 * RAM usage chart composable that displays a real-time curve with filled area
 */
@Composable
fun RamUsageChart(
    dataPoints: List<RamUsageUtils.ChartDataPoint>,
    minPercentage: Float,
    maxPercentage: Float,
    currentRamInfo: RamUsageUtils.RamUsageInfo?,
    modifier: Modifier = Modifier
) {
    val chartColor = Color(0xFF6C35D3) // Light blue color like in the image
    val fillColor = chartColor.copy(alpha = 0.3f)
    val backgroundColor = Color(0xFFFFF5FD) // Light gray background

    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(200.dp)
            .background(backgroundColor, RoundedCornerShape(12.dp))
            .border(1.5.dp,chartColor,RoundedCornerShape(12.dp))
            .padding(16.dp)
    ) {
        // Always show the chart area, even with minimal data
        Canvas(
            modifier = Modifier.fillMaxSize()
        ) {
            drawRamUsageChart(
                dataPoints = dataPoints,
                minPercentage = minPercentage,
                maxPercentage = maxPercentage,
                chartColor = chartColor,
                fillColor = fillColor,
                canvasSize = size
            )
        }

        // Memory usage labels
//        if (currentRamInfo != null) {
//            // Used memory label (top right)
//            Text(
//                text = "${currentRamInfo.usedMemoryFormatted} Used",
//                fontSize = 12.sp,
//                color = Color.Gray,
//                modifier = Modifier
//                    .align(Alignment.TopEnd)
//                    .padding(top = 8.dp, end = 8.dp)
//            )
//
//            // Available memory label (bottom right)
//            Text(
//                text = "${currentRamInfo.availableMemoryFormatted} available",
//                fontSize = 12.sp,
//                color = Color.Gray,
//                modifier = Modifier
//                    .align(Alignment.BottomEnd)
//                    .padding(bottom = 8.dp, end = 8.dp)
//            )
//        }
    }
}

/**
 * Draw the RAM usage chart on canvas with filled area
 */
private fun DrawScope.drawRamUsageChart(
    dataPoints: List<RamUsageUtils.ChartDataPoint>,
    minPercentage: Float,
    maxPercentage: Float,
    chartColor: Color,
    fillColor: Color,
    canvasSize: androidx.compose.ui.geometry.Size
) {
    val width = canvasSize.width
    val height = canvasSize.height
    val padding = 20f

    // Calculate drawing area
    val drawingWidth = width - (padding * 2)
    val drawingHeight = height - (padding * 2)

    // If no data points, create a sample curve for initial display
    val effectiveDataPoints = if (dataPoints.isEmpty()) {
        // Create sample data points for initial display
        listOf(
            RamUsageUtils.ChartDataPoint(45f, System.currentTimeMillis()),
            RamUsageUtils.ChartDataPoint(48f, System.currentTimeMillis()),
            RamUsageUtils.ChartDataPoint(52f, System.currentTimeMillis()),
            RamUsageUtils.ChartDataPoint(49f, System.currentTimeMillis()),
            RamUsageUtils.ChartDataPoint(55f, System.currentTimeMillis())
        )
    } else if (dataPoints.size == 1) {
        // If only one data point, create a small curve around it
        val percentage = dataPoints[0].percentage
        listOf(
            RamUsageUtils.ChartDataPoint(percentage - 2f, System.currentTimeMillis()),
            RamUsageUtils.ChartDataPoint(percentage, System.currentTimeMillis()),
            RamUsageUtils.ChartDataPoint(percentage + 1f, System.currentTimeMillis())
        )
    } else {
        dataPoints
    }

    // Calculate step size for X axis
    val stepX = if (effectiveDataPoints.size > 1) {
        drawingWidth / (effectiveDataPoints.size - 1)
    } else {
        drawingWidth
    }

    // Create paths for the curve and fill
    val curvePath = Path()
    val fillPath = Path()
    val points = mutableListOf<Offset>()

    // Calculate points
    effectiveDataPoints.forEachIndexed { index, dataPoint ->
        val x = padding + (index * stepX)

        // Normalize percentage to drawing area
        val normalizedPercentage = if (maxPercentage > minPercentage) {
            (dataPoint.percentage - minPercentage) / (maxPercentage - minPercentage)
        } else {
            0.5f // Center if no range
        }

        val y = padding + drawingHeight - (normalizedPercentage * drawingHeight)

        points.add(Offset(x, y))
    }

    // Draw the filled area and curve
    if (points.isNotEmpty()) {
        // Create fill path (area under the curve)
        fillPath.moveTo(points[0].x, height - padding) // Start from bottom
        fillPath.lineTo(points[0].x, points[0].y) // Go to first point

        // Create curve path
        curvePath.moveTo(points[0].x, points[0].y)

        // Add curve points
        for (i in 1 until points.size) {
            val currentPoint = points[i]
            val previousPoint = points[i - 1]

            if (points.size == 2 || i == 1) {
                // Simple line for first segment or only two points
                curvePath.lineTo(currentPoint.x, currentPoint.y)
                fillPath.lineTo(currentPoint.x, currentPoint.y)
            } else {
                // Create smooth curve
                val controlX = (previousPoint.x + currentPoint.x) / 2
                val controlY = (previousPoint.y + currentPoint.y) / 2
                curvePath.quadraticBezierTo(
                    controlX, previousPoint.y,
                    currentPoint.x, currentPoint.y
                )
                fillPath.quadraticBezierTo(
                    controlX, previousPoint.y,
                    currentPoint.x, currentPoint.y
                )
            }
        }

        // Complete the fill path
        fillPath.lineTo(points.last().x, height - padding) // Go to bottom
        fillPath.close() // Close the path

        // Draw the filled area
        drawPath(
            path = fillPath,
            color = fillColor
        )

        // Draw the curve line
        drawPath(
            path = curvePath,
            color = chartColor,
            style = Stroke(
                width = 2.dp.toPx(),
                cap = StrokeCap.Round
            )
        )

        // Draw data points (always show for visual feedback)
        points.forEach { point ->
            drawCircle(
                color = chartColor,
                radius = 3.dp.toPx(),
                center = point
            )
        }
    }
}