package com.example.clean0522.ui.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.clean0522.R
import com.example.clean0522.utils.RamUsageUtils

/**
 * RAM usage chart composable that displays a real-time curve with filled area
 */
@Composable
fun RamUsageChart(
    dataPoints: List<RamUsageUtils.ChartDataPoint>,
    minMemoryBytes: Long,
    maxMemoryBytes: Long,
    currentRamInfo: RamUsageUtils.RamUsageInfo?,
    modifier: Modifier = Modifier
) {
    val chartColor = Color(0xFF8A5CF6) // Purple color to match the design
    val backgroundColor = Color(0xFFFFF5FD) // Light gray background

    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(200.dp)
            .background(backgroundColor, RoundedCornerShape(12.dp))
            .border(1.5.dp,chartColor,RoundedCornerShape(12.dp))
            .padding(16.dp)
    ) {
        // 只有当有数据点时才显示图表
        if (dataPoints.isNotEmpty()) {
            Canvas(
                modifier = Modifier.fillMaxSize()
            ) {
                drawProgressiveRamChart(
                    dataPoints = dataPoints,
                    minMemoryBytes = minMemoryBytes,
                    maxMemoryBytes = maxMemoryBytes,
                    chartColor = chartColor,
                    canvasSize = size
                )
            }
        } else {
            // 没有数据时显示空状态
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "等待数据...",
                    color = Color.Gray,
                    fontSize = 14.sp
                )
            }
        }

        // Memory usage labels
//        if (currentRamInfo != null) {
//            // Used memory label (top right)
//            Text(
//                text = "${currentRamInfo.usedMemoryFormatted} Used",
//                fontSize = 12.sp,
//                color = Color.Gray,
//                modifier = Modifier
//                    .align(Alignment.TopEnd)
//                    .padding(top = 8.dp, end = 8.dp)
//            )
//
//            // Available memory label (bottom right)
//            Text(
//                text = "${currentRamInfo.availableMemoryFormatted} available",
//                fontSize = 12.sp,
//                color = Color.Gray,
//                modifier = Modifier
//                    .align(Alignment.BottomEnd)
//                    .padding(bottom = 8.dp, end = 8.dp)
//            )
//        }
    }
}

/**
 * Draw the progressive RAM usage chart - shows data points from left to right
 */
private fun DrawScope.drawProgressiveRamChart(
    dataPoints: List<RamUsageUtils.ChartDataPoint>,
    minMemoryBytes: Long,
    maxMemoryBytes: Long,
    chartColor: Color,
    canvasSize: androidx.compose.ui.geometry.Size
) {
    val width = canvasSize.width
    val height = canvasSize.height
    val padding = 20f

    // Calculate drawing area
    val drawingWidth = width - (padding * 2)
    val drawingHeight = height - (padding * 2)

    // 直接使用实际数据点，不创建虚假数据
    if (dataPoints.isEmpty()) return

    // 计算每个数据点在X轴上的位置
    // 使用固定的10个位置，从左到右逐渐填充
    val maxPoints = 10
    val stepX = drawingWidth / (maxPoints - 1)

    // Create paths for the curve and fill
    val curvePath = Path()
    val fillPath = Path()
    val points = mutableListOf<Offset>()

    // 计算实际数据点的位置 - 从左到右按顺序排列
    dataPoints.forEachIndexed { index, dataPoint ->
        // 从左边开始，每个新数据点占据下一个位置
        val x = padding + (index * stepX)

        // 标准化内存值到绘制区域
        val normalizedValue = if (maxMemoryBytes > minMemoryBytes) {
            (dataPoint.usedMemoryBytes - minMemoryBytes).toFloat() / (maxMemoryBytes - minMemoryBytes).toFloat()
        } else {
            0.5f // 如果没有范围则居中
        }

        val y = padding + drawingHeight - (normalizedValue * drawingHeight)
        points.add(Offset(x, y))
    }

    // 只有当有足够的点时才绘制曲线
    if (points.isNotEmpty()) {
        when (points.size) {
            1 -> {
                // 只有一个点时，绘制一个小圆点
                drawCircle(
                    color = chartColor,
                    radius = 4.dp.toPx(),
                    center = points[0]
                )
            }
            else -> {
                // 多个点时绘制曲线和填充
                // 创建填充路径（曲线下方的区域）
                fillPath.moveTo(points[0].x, height - padding) // 从底部开始
                fillPath.lineTo(points[0].x, points[0].y) // 到第一个点

                // 创建曲线路径
                curvePath.moveTo(points[0].x, points[0].y)

                // 添加平滑曲线点
                for (i in 1 until points.size) {
                    val currentPoint = points[i]
                    val previousPoint = points[i - 1]

                    if (points.size == 2) {
                        // 两个点时使用直线
                        curvePath.lineTo(currentPoint.x, currentPoint.y)
                        fillPath.lineTo(currentPoint.x, currentPoint.y)
                    } else {
                        // 多个点时使用三次贝塞尔曲线
                        val controlPoint1X = previousPoint.x + (currentPoint.x - previousPoint.x) * 0.3f
                        val controlPoint1Y = previousPoint.y
                        val controlPoint2X = currentPoint.x - (currentPoint.x - previousPoint.x) * 0.3f
                        val controlPoint2Y = currentPoint.y

                        curvePath.cubicTo(
                            controlPoint1X, controlPoint1Y,
                            controlPoint2X, controlPoint2Y,
                            currentPoint.x, currentPoint.y
                        )
                        fillPath.cubicTo(
                            controlPoint1X, controlPoint1Y,
                            controlPoint2X, controlPoint2Y,
                            currentPoint.x, currentPoint.y
                        )
                    }
                }

                // 完成填充路径
                fillPath.lineTo(points.last().x, height - padding) // 到底部
                fillPath.close() // 闭合路径

                // 创建渐变填充
                val gradient = Brush.verticalGradient(
                    colors = listOf(
                        Color(0x94FF80BB), // #FF80BB with 58% opacity
                        Color(0x00FF70B2)  // #FF70B2 with 0% opacity
                    ),
                    startY = padding,
                    endY = height - padding
                )

                // 绘制填充区域
                drawPath(
                    path = fillPath,
                    brush = gradient
                )

                // 绘制曲线
                drawPath(
                    path = curvePath,
                    color = chartColor,
                    style = Stroke(
                        width = 3.dp.toPx(),
                        cap = StrokeCap.Round
                    )
                )
            }
        }
    }
}