package com.example.clean0522.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.clean0522.R
import com.example.clean0522.utils.RamUsageUtils

/**
 * RAM usage chart composable that displays a real-time curve with filled area
 */
@Composable
fun RamUsageChart(
    dataPoints: List<RamUsageUtils.ChartDataPoint>,
    minMemoryBytes: Long,
    maxMemoryBytes: Long,
    currentRamInfo: RamUsageUtils.RamUsageInfo?,
    modifier: Modifier = Modifier
) {
    val chartColor = Color(0xFF8A5CF6) // Purple color to match the design
    val backgroundColor = Color(0xFFFFF5FD) // Light gray background

    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(200.dp)
            .background(backgroundColor, RoundedCornerShape(12.dp))
            .border(1.5.dp,chartColor,RoundedCornerShape(12.dp))
            .padding(16.dp)
    ) {
        // Always show the chart area, even with minimal data
        Canvas(
            modifier = Modifier.fillMaxSize()
        ) {
            drawRamUsageChart(
                dataPoints = dataPoints,
                minMemoryBytes = minMemoryBytes,
                maxMemoryBytes = maxMemoryBytes,
                chartColor = chartColor,
                canvasSize = size
            )
        }

        // Memory usage labels
//        if (currentRamInfo != null) {
//            // Used memory label (top right)
//            Text(
//                text = "${currentRamInfo.usedMemoryFormatted} Used",
//                fontSize = 12.sp,
//                color = Color.Gray,
//                modifier = Modifier
//                    .align(Alignment.TopEnd)
//                    .padding(top = 8.dp, end = 8.dp)
//            )
//
//            // Available memory label (bottom right)
//            Text(
//                text = "${currentRamInfo.availableMemoryFormatted} available",
//                fontSize = 12.sp,
//                color = Color.Gray,
//                modifier = Modifier
//                    .align(Alignment.BottomEnd)
//                    .padding(bottom = 8.dp, end = 8.dp)
//            )
//        }
    }
}

/**
 * Draw the RAM usage chart on canvas with filled area
 */
private fun DrawScope.drawRamUsageChart(
    dataPoints: List<RamUsageUtils.ChartDataPoint>,
    minMemoryBytes: Long,
    maxMemoryBytes: Long,
    chartColor: Color,
    canvasSize: androidx.compose.ui.geometry.Size
) {
    val width = canvasSize.width
    val height = canvasSize.height
    val padding = 20f

    // Calculate drawing area
    val drawingWidth = width - (padding * 2)
    val drawingHeight = height - (padding * 2)

    // If no data points, create a sample curve for initial display
    val effectiveDataPoints = if (dataPoints.isEmpty()) {
        // Create sample data points for initial display
        val sampleBase = 2L * 1024L * 1024L * 1024L // 2GB base
        listOf(
            RamUsageUtils.ChartDataPoint(sampleBase + 100L * 1024L * 1024L, System.currentTimeMillis()),
            RamUsageUtils.ChartDataPoint(sampleBase + 150L * 1024L * 1024L, System.currentTimeMillis()),
            RamUsageUtils.ChartDataPoint(sampleBase + 200L * 1024L * 1024L, System.currentTimeMillis()),
            RamUsageUtils.ChartDataPoint(sampleBase + 120L * 1024L * 1024L, System.currentTimeMillis()),
            RamUsageUtils.ChartDataPoint(sampleBase + 180L * 1024L * 1024L, System.currentTimeMillis())
        )
    } else if (dataPoints.size == 1) {
        // If only one data point, create a small curve around it
        val memoryBytes = dataPoints[0].usedMemoryBytes
        val variation = 20L * 1024L * 1024L // 20MB variation for sensitivity

        listOf(
            RamUsageUtils.ChartDataPoint(memoryBytes - variation, System.currentTimeMillis()),
            RamUsageUtils.ChartDataPoint(memoryBytes, System.currentTimeMillis()),
            RamUsageUtils.ChartDataPoint(memoryBytes + variation / 2, System.currentTimeMillis())
        )
    } else {
        dataPoints
    }

    // Calculate step size for X axis
    val stepX = drawingWidth / (effectiveDataPoints.size - 1)

    // Create paths for the curve and fill
    val curvePath = Path()
    val fillPath = Path()
    val points = mutableListOf<Offset>()

    // Calculate points
    effectiveDataPoints.forEachIndexed { index, dataPoint ->
        val x = padding + (index * stepX)

        // Normalize percentage to drawing area
        val normalizedValue = if (maxMemoryBytes > minMemoryBytes) {
            (dataPoint.usedMemoryBytes - minMemoryBytes).toFloat() / (maxMemoryBytes - minMemoryBytes).toFloat()
        } else {
            0.5f // Center if no range
        }

        val y = padding + drawingHeight - (normalizedValue * drawingHeight)

        points.add(Offset(x, y))
    }

    // Draw the filled area and curve
    if (points.isNotEmpty()) {
        // Create fill path (area under the curve)
        fillPath.moveTo(points[0].x, height - padding) // Start from bottom
        fillPath.lineTo(points[0].x, points[0].y) // Go to first point

        // Create curve path
        curvePath.moveTo(points[0].x, points[0].y)

        // Add smooth curve points using cubic bezier for better smoothness
        if (points.size >= 2) {
            for (i in 1 until points.size) {
                val currentPoint = points[i]
                val previousPoint = points[i - 1]

                if (points.size == 2) {
                    // Simple line for only two points
                    curvePath.lineTo(currentPoint.x, currentPoint.y)
                    fillPath.lineTo(currentPoint.x, currentPoint.y)
                } else {
                    // Create smooth curve using cubic bezier
                    val controlPoint1X = previousPoint.x + (currentPoint.x - previousPoint.x) * 0.3f
                    val controlPoint1Y = previousPoint.y
                    val controlPoint2X = currentPoint.x - (currentPoint.x - previousPoint.x) * 0.3f
                    val controlPoint2Y = currentPoint.y

                    curvePath.cubicTo(
                        controlPoint1X, controlPoint1Y,
                        controlPoint2X, controlPoint2Y,
                        currentPoint.x, currentPoint.y
                    )
                    fillPath.cubicTo(
                        controlPoint1X, controlPoint1Y,
                        controlPoint2X, controlPoint2Y,
                        currentPoint.x, currentPoint.y
                    )
                }
            }
        }

        // Complete the fill path
        fillPath.lineTo(points.last().x, height - padding) // Go to bottom
        fillPath.close() // Close the path

        // Create gradient for fill
        val gradient = Brush.verticalGradient(
            colors = listOf(
                Color(0x94FF80BB), // #FF80BB with 58% opacity (0.58 * 255 = 148 = 0x94)
                Color(0x00FF70B2)  // #FF70B2 with 0% opacity
            ),
            startY = padding,
            endY = height - padding
        )

        // Draw the filled area with gradient
        drawPath(
            path = fillPath,
            brush = gradient
        )

        // Draw the curve line
        drawPath(
            path = curvePath,
            color = chartColor,
            style = Stroke(
                width = 3.dp.toPx(),
                cap = StrokeCap.Round
            )
        )

        // Remove data points drawing - no dots should be visible
    }
}