package com.example.clean0522

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Sort
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.graphics.drawable.toBitmap
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.clean0522.ui.components.SortDialog
import com.example.clean0522.ui.navigation.TopNavBar
import com.example.clean0522.ui.theme.Clean0522Theme
import com.example.clean0522.utils.AppManagerUtils
import com.example.clean0522.viewmodel.AppManagerViewModel

/**
 * Activity for app management tools
 */
class AppInfoActivity : ComponentBaseActivity() {

    companion object {
        const val EXTRA_APP_TYPE = "app_type"
        const val TYPE_APP_MANAGER = "app_manager"
    }

    @Composable
    override fun setHomePage() {
        val appType = intent.getStringExtra(EXTRA_APP_TYPE) ?: TYPE_APP_MANAGER
        Clean0522Theme {
            AppInfoScreen(
                appType = appType,
                onBackClick = { finish() }
            )
        }
    }
}

/**
 * App info screen composable
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppInfoScreen(
    appType: String,
    onBackClick: () -> Unit
) {
    val title = when (appType) {
        AppInfoActivity.TYPE_APP_MANAGER -> stringResource(R.string.tool_app_manager)
        else -> stringResource(R.string.app_manager_title)
    }

    val viewModel: AppManagerViewModel = viewModel()
    val showSortDialog by viewModel.showSortDialog.collectAsState()
    val currentSortOption by viewModel.currentSortOption.collectAsState()

    Scaffold(
        modifier = Modifier.navigationBarsPadding(),
        containerColor = colorResource(R.color.bg_color),
        topBar = {
            TopNavBar(
                title = title,
                showBackButton = true,
                backButtonAction = onBackClick,
                settingsButtonContent = {
                    Image(painter = painterResource(R.mipmap.ic_sort),
                        contentDescription = stringResource(R.string.sort),
                        modifier = Modifier.size(24.dp)
                            .clickable {
                                viewModel.showSortDialog()
                            }
                    )
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when (appType) {
                AppInfoActivity.TYPE_APP_MANAGER -> AppManagerContent(viewModel = viewModel)
                else -> PlaceholderContent(title)
            }
        }
    }

    // Sort dialog
    if (showSortDialog) {
        SortDialog(
            currentSortOption = currentSortOption,
            onSortOptionSelected = { sortOption ->
                viewModel.updateSortOption(sortOption)
            },
            onDismiss = { viewModel.hideSortDialog() }
        )
    }
}

/**
 * App manager content with tabs and app list
 */
@Composable
fun AppManagerContent(viewModel: AppManagerViewModel) {
    val context = LocalContext.current
    val isLoading by viewModel.isLoading.collectAsState()
    val allApps by viewModel.allApps.collectAsState()
    val systemApps by viewModel.systemApps.collectAsState()
    val installedApps by viewModel.installedApps.collectAsState()
    val allAppsCount by viewModel.allAppsCount.collectAsState()
    val systemAppsCount by viewModel.systemAppsCount.collectAsState()
    val installedAppsCount by viewModel.installedAppsCount.collectAsState()

    var selectedTabIndex by remember { mutableStateOf(0) }

    // Load apps when first composed
    LaunchedEffect(Unit) {
        viewModel.loadApps(context)
    }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Tab Row
        TabRow(
            selectedTabIndex = selectedTabIndex,
            modifier = Modifier.fillMaxWidth(),
            containerColor = MaterialTheme.colorScheme.surface
        ) {
            Tab(
                selected = selectedTabIndex == 0,
                onClick = { selectedTabIndex = 0 },
                text = {
                    Text(
                        text = stringResource(R.string.tab_all),
                        fontSize = 16.sp,
                        fontWeight = if (selectedTabIndex == 0) FontWeight.Bold else FontWeight.Normal
                    )
                }
            )
            Tab(
                selected = selectedTabIndex == 1,
                onClick = { selectedTabIndex = 1 },
                text = {
                    Text(
                        text = stringResource(R.string.tab_system_apps),
                        fontSize = 16.sp,
                        fontWeight = if (selectedTabIndex == 1) FontWeight.Bold else FontWeight.Normal
                    )
                }
            )
            Tab(
                selected = selectedTabIndex == 2,
                onClick = { selectedTabIndex = 2 },
                text = {
                    Text(
                        text = stringResource(R.string.tab_installed_apps),
                        fontSize = 16.sp,
                        fontWeight = if (selectedTabIndex == 2) FontWeight.Bold else FontWeight.Normal
                    )
                }
            )
        }

        // Content based on selected tab
        when (selectedTabIndex) {
            0 -> AppListContent(
                apps = allApps,
                isLoading = isLoading,
                appsCount = allAppsCount,
                onAppClick = { app ->
                    val intent = AppDetailActivity.createIntent(context, app.packageName, app.appName)
                    context.startActivity(intent)
                }
            )
            1 -> AppListContent(
                apps = systemApps,
                isLoading = isLoading,
                appsCount = systemAppsCount,
                onAppClick = { app ->
                    val intent = AppDetailActivity.createIntent(context, app.packageName, app.appName)
                    context.startActivity(intent)
                }
            )
            2 -> AppListContent(
                apps = installedApps,
                isLoading = isLoading,
                appsCount = installedAppsCount,
                onAppClick = { app ->
                    val intent = AppDetailActivity.createIntent(context, app.packageName, app.appName)
                    context.startActivity(intent)
                }
            )
        }
    }
}

/**
 * App list content composable
 */
@Composable
fun AppListContent(
    apps: List<AppManagerUtils.AppInfo>,
    isLoading: Boolean,
    appsCount: Int,
    onAppClick: (AppManagerUtils.AppInfo) -> Unit
) {
    if (isLoading) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator()
        }
    } else if (apps.isEmpty()) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = stringResource(R.string.no_apps_found),
                fontSize = 16.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    } else {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(apps) { app ->
                AppItem(
                    app = app,
                    onClick = { onAppClick(app) }
                )
            }
        }
    }
}

/**
 * Individual app item composable
 */
@Composable
fun AppItem(
    app: AppManagerUtils.AppInfo,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // App icon
            if (app.icon != null) {
                Image(
                    bitmap = app.icon.toBitmap(64, 64).asImageBitmap(),
                    contentDescription = app.appName,
                    modifier = Modifier
                        .size(48.dp)
                        .clip(RoundedCornerShape(8.dp))
                )
            } else {
                Box(
                    modifier = Modifier
                        .size(48.dp)
                        .background(
                            color = MaterialTheme.colorScheme.surfaceVariant,
                            shape = RoundedCornerShape(8.dp)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = app.appName.take(1).uppercase(),
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            Spacer(modifier = Modifier.width(16.dp))

            // App info
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = app.appName,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = app.installDate + " " + app.sizeFormatted,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // More button
            Button(
                onClick = onClick,
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF4DD0E1) // Light blue color from the image
                ),
                shape = RoundedCornerShape(20.dp),
                modifier = Modifier.height(36.dp),
                contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp)
            ) {
                Text(
                    text = stringResource(R.string.more),
                    color = Color.White,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

/**
 * Placeholder content for unknown app types
 */
@Composable
fun PlaceholderContent(title: String) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = title,
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold
        )
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = stringResource(R.string.coming_soon),
            fontSize = 16.sp
        )
    }
}
