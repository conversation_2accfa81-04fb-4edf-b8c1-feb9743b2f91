package com.example.clean0522

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.painter.BitmapPainter
import androidx.core.graphics.drawable.toBitmap
import android.opengl.GLES20
import android.opengl.GLSurfaceView
import androidx.compose.foundation.BorderStroke
import javax.microedition.khronos.egl.EGLConfig
import javax.microedition.khronos.opengles.GL10
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.unit.times
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.example.clean0522.ui.components.RamUsageChart
import com.example.clean0522.ui.navigation.TopNavBar
import com.example.clean0522.ui.theme.Clean0522Theme
import com.example.clean0522.utils.AppProcessUtils
import com.example.clean0522.utils.BatteryInfoUtils
import com.example.clean0522.utils.CpuInfoUtils
import com.example.clean0522.utils.DeviceInfoUtils
import com.example.clean0522.utils.NetworkInfoUtils
import com.example.clean0522.utils.RamUsageUtils
import com.example.clean0522.utils.logD
import com.example.clean0522.utils.logE
import com.example.clean0522.viewmodel.AppProcessViewModel
import com.example.clean0522.viewmodel.BatteryInfoViewModel
import com.example.clean0522.viewmodel.CpuInfoViewModel
import com.example.clean0522.viewmodel.NetworkInfoViewModel
import com.example.clean0522.viewmodel.RamUsageViewModel
import kotlinx.coroutines.flow.update

/**
 * Activity for system information and monitoring tools
 */
class SystemInfoActivity : ComponentBaseActivity() {

    companion object {
        const val EXTRA_INFO_TYPE = "info_type"
        const val TYPE_DEVICE_INFO = "device_info"
        const val TYPE_RAM_USAGE = "ram_usage"
        const val TYPE_BATTERY_INFO = "battery_info"
        const val TYPE_CPU_MONITOR = "cpu_monitor"
        const val TYPE_NETWORK = "network"
        const val TYPE_APP_PROCESS = "app_process"
    }

    @Composable
    override fun setHomePage() {
        val infoType = intent.getStringExtra(EXTRA_INFO_TYPE) ?: TYPE_DEVICE_INFO
        Clean0522Theme {
            SystemInfoScreen(
                infoType = infoType,
                onBackClick = { finish() }
            )
        }
    }
}

/**
 * System info screen composable
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SystemInfoScreen(
    infoType: String,
    onBackClick: () -> Unit
) {
    val title = when (infoType) {
        SystemInfoActivity.TYPE_DEVICE_INFO -> stringResource(R.string.tool_device_info)
        SystemInfoActivity.TYPE_RAM_USAGE -> stringResource(R.string.tool_ram_usage)
        SystemInfoActivity.TYPE_BATTERY_INFO -> stringResource(R.string.tool_battery_info)
        SystemInfoActivity.TYPE_CPU_MONITOR -> stringResource(R.string.tool_cpu_monitor)
        SystemInfoActivity.TYPE_NETWORK -> stringResource(R.string.tool_network)
        SystemInfoActivity.TYPE_APP_PROCESS -> stringResource(R.string.tool_app_process)
        else -> stringResource(R.string.system_info_title)
    }

    Scaffold(
        modifier = Modifier.navigationBarsPadding(),
        containerColor = colorResource(R.color.bg_color),
        topBar = {
            TopNavBar(
                title = title,
                showBackButton = true,
                backButtonAction = onBackClick,
                settingsButtonContent = {}
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentAlignment = Alignment.Center
        ) {
            when (infoType) {
                SystemInfoActivity.TYPE_DEVICE_INFO -> DeviceInfoContent()
                SystemInfoActivity.TYPE_RAM_USAGE -> RamUsageContent()
                SystemInfoActivity.TYPE_BATTERY_INFO -> BatteryInfoContent()
                SystemInfoActivity.TYPE_CPU_MONITOR -> CpuMonitorContent()
                SystemInfoActivity.TYPE_NETWORK -> NetworkContent()
                SystemInfoActivity.TYPE_APP_PROCESS -> AppProcessContent()
                else -> PlaceholderContent(title)
            }
        }
    }
}

/**
 * Device info content with real device information
 */
@Composable
fun DeviceInfoContent() {
    val context = LocalContext.current

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // Device header with icon and basic info
        item {
            DeviceHeaderCard()
        }

        // Device information cards
        item {
            Card(
                colors = CardDefaults.cardColors(containerColor = Color.White),
                modifier = Modifier.fillMaxWidth(), elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                DeviceInfoUtils.getAllDeviceInfoItems(context).forEach { item ->
                    DeviceInfoCard(
                        icon = item.icon,
                        title = item.title,
                        value = item.value
                    )
                }
            }

            Spacer(modifier = Modifier.height(6.dp))
        }
    }
}

/**
 * Device header card showing device icon and basic info
 */
@Composable
fun DeviceHeaderCard() {

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .paint(painter = painterResource(id = R.drawable.bg_device), contentScale = ContentScale.Fit)
            .padding(20.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        // Device icon placeholder

        Image(
            painter = painterResource(R.mipmap.tool_device),
            contentDescription = stringResource(R.string.device_image_placeholder),
            modifier = Modifier.size(75.dp),
        )

        Spacer(modifier = Modifier.width(16.dp))

        // Device basic info
        Column {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Box(modifier = Modifier
                    .width(2.dp)
                    .height(16.dp)
                    .background(colorResource(R.color.text_black),RoundedCornerShape(2.dp))
                )

                Spacer(modifier = Modifier.width(6.dp))

                Text(
                    text = DeviceInfoUtils.getBrand(),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = colorResource(R.color.text_black)
                )
            }

            Spacer(modifier = Modifier.height(6.dp))


            Row(verticalAlignment = Alignment.CenterVertically) {
                Box(modifier = Modifier
                    .width(2.dp)
                    .height(16.dp)
                    .background(colorResource(R.color.text_black),RoundedCornerShape(2.dp))
                )

                Spacer(modifier = Modifier.width(6.dp))

                Text(
                    text = DeviceInfoUtils.getModel(),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = colorResource(R.color.text_black)
                )
            }
        }
    }
}

/**
 * RAM usage content with real-time monitoring
 */
@Composable
fun RamUsageContent() {
    val context = LocalContext.current
    val viewModel: RamUsageViewModel = viewModel()
    val ramUsageInfo by viewModel.ramUsageInfo.collectAsState()
    val isMonitoring by viewModel.isMonitoring.collectAsState()

    // Start monitoring when the composable is first composed
    LaunchedEffect(Unit) {
        viewModel.startMonitoring(context)
    }

    // Stop monitoring when the composable is disposed
    DisposableEffect(Unit) {
        onDispose {
            viewModel.stopMonitoring()
        }
    }

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // RAM usage header
        item {
            RamUsageHeader(ramUsageInfo)
        }

        // RAM usage chart
        item {
            val dataPoints = viewModel.chartDataManager.getDataPoints()
            val (minPercentage, maxPercentage) = viewModel.chartDataManager.getMinMaxPercentage()


            RamUsageChart(
                dataPoints = dataPoints,
                minPercentage = minPercentage,
                maxPercentage = maxPercentage,
                currentRamInfo = ramUsageInfo,
                modifier = Modifier.fillMaxWidth()
            )


        }

        // Feature suggestions
        item {
            FeatureSuggestionsSection()
        }
    }
}

/**
 * Battery info content with real-time monitoring
 */
@Composable
fun BatteryInfoContent() {
    val context = LocalContext.current
    val viewModel: BatteryInfoViewModel = viewModel()
    val batteryInfo by viewModel.batteryInfo.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.startMonitoring(context)
    }

    DisposableEffect(Unit) {
        onDispose {
            viewModel.stopMonitoring()
        }
    }

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Battery header
        item {
            BatteryInfoHeader(batteryInfo)
        }

        if (batteryInfo != null) {
            items(BatteryInfoUtils.getBatteryInfoItems(context, batteryInfo!!)) { item ->
                DeviceInfoCard(
                    icon = item.icon,
                    title = item.title,
                    value = item.value
                )
            }
        }

        item {
            BatteryFeatureSuggestionsSection()
        }
    }
}

/**
 * CPU monitor content with real-time monitoring
 */
@Composable
fun CpuMonitorContent() {
    val context = LocalContext.current
    val viewModel: CpuInfoViewModel = viewModel()
    val cpuInfo by viewModel.cpuInfo.collectAsState()
    val gpuRenderer by viewModel.gpuRenderer.collectAsState()
    val gpuVendor by viewModel.gpuVendor.collectAsState()
    val gpuVersion by viewModel.gpuVersion.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.startMonitoring(context)
    }

    DisposableEffect(Unit) {
        onDispose {
            viewModel.stopMonitoring()
        }
    }

    val cpuGpuInfoItems = if (cpuInfo != null) {
        remember(cpuInfo, gpuRenderer, gpuVendor, gpuVersion) {
            CpuInfoUtils.getCpuGpuInfoItems(context, cpuInfo!!, viewModel.getGpuInfo())
        }
    } else {
        emptyList()
    }

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        item {
            GLSurfaceViewComposable(viewModel)
        }

        if (cpuInfo != null) {
            item {
                CpuCoresHeader(cpuInfo!!.cores)
            }
        }

        if (cpuInfo != null) {
            item {
                CpuCoresGrid(cpuInfo!!.cores)
            }
        }

        if (cpuInfo != null) {
            items(
                items = cpuGpuInfoItems,
                key = { item -> item.title }
            ) { item ->
                DeviceInfoCard(
                    icon = item.icon,
                    title = item.title,
                    value = item.value
                )
            }
        }
    }
}

/**
 * Network content with real-time monitoring
 */
@Composable
fun NetworkContent() {
    val context = LocalContext.current
    val viewModel: NetworkInfoViewModel = viewModel()
    val networkInfo by viewModel.networkInfo.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.startMonitoring(context)
    }

    DisposableEffect(Unit) {
        onDispose {
            viewModel.stopMonitoring()
        }
    }

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // WiFi Section
        item {
            NetworkSectionHeader(
                title = stringResource(R.string.wifi_section),
                icon = Icons.Default.Wifi
            )
        }

        if (networkInfo?.wifiInfo?.isConnected == true) {
            val wifiItems = NetworkInfoUtils.getWiFiInfoItems(context, networkInfo!!.wifiInfo)
            if (wifiItems.isNotEmpty()) {
                items(wifiItems) { item ->
                    DeviceInfoCard(
                        icon = R.mipmap.c_abi,
                        title = item.first,
                        value = item.second
                    )
                }
            } else {
                item {
                    NetworkDisconnectedCard(
                        message = stringResource(R.string.wifi_not_connected)
                    )
                }
            }
        } else {
            item {
                NetworkDisconnectedCard(
                    message = stringResource(R.string.wifi_not_connected)
                )
            }
        }

        // Mobile Data Section
        item {
            Spacer(modifier = Modifier.height(8.dp))
            NetworkSectionHeader(
                title = stringResource(R.string.mobile_data_section),
                icon = Icons.Default.SignalCellularAlt
            )
        }

        if (networkInfo?.mobileDataInfo != null) {
            val mobileDataItems = NetworkInfoUtils.getMobileDataInfoItems(context, networkInfo!!.mobileDataInfo)
            if (mobileDataItems.isNotEmpty()) {
                items(mobileDataItems) { item ->
                    DeviceInfoCard(
                        icon = R.mipmap.c_abi,
                        title = item.first,
                        value = item.second
                    )
                }
            } else {
                item {
                    NetworkDisconnectedCard(
                        message = stringResource(R.string.mobile_data_not_available)
                    )
                }
            }
        } else {
            item {
                NetworkDisconnectedCard(
                    message = stringResource(R.string.mobile_data_not_available)
                )
            }
        }
    }
}

/**
 * Network section header
 */
@Composable
fun NetworkSectionHeader(
    title: String,
    icon: ImageVector
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Network icon
            Box(
                modifier = Modifier
                    .size(60.dp)
                    .background(
                        color = MaterialTheme.colorScheme.surfaceVariant,
                        shape = RoundedCornerShape(8.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = title,
                    modifier = Modifier.size(30.dp),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // Section title
            Text(
                text = title,
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface
            )
        }
    }
}

/**
 * Network disconnected card
 */
@Composable
fun NetworkDisconnectedCard(message: String) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = message,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * Device info card composable matching the mockup design
 */
@Composable
fun DeviceInfoCard(
    icon: Int,
    title: String,
    value: String
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {

        Image(painter = painterResource(id = icon),
            contentDescription = null,
            modifier = Modifier.size(30.dp))

        Spacer(modifier = Modifier.width(4.dp))

        Text(
            text = title,
            fontSize = 13.sp,
            lineHeight = 16.sp,
            fontWeight = FontWeight.Medium,
            color = colorResource(R.color.text_gray_70),
            modifier = Modifier.weight(1f)
        )
        Text(
            text = value,
            fontSize = 15.sp,
            fontWeight = FontWeight.Medium,
            color = colorResource(R.color.text_black),
            textAlign = TextAlign.End,
            modifier = Modifier.padding(start = 4.dp)
                .weight(1.2f)
        )
    }
}

/**
 * RAM usage header showing current usage statistics
 */
@Composable
fun RamUsageHeader(ramUsageInfo: RamUsageUtils.RamUsageInfo?) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .paint(painter = painterResource(R.drawable.bg_ram), contentScale = ContentScale.Fit)
                .padding(20.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {

            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Image(
                    painter = painterResource(R.mipmap.tool_ram),
                    contentDescription = stringResource(R.string.device_image_placeholder),
                    modifier = Modifier.size(75.dp),
                )

                Spacer(modifier = Modifier.height(4.dp))

                if (ramUsageInfo != null) {
                    Text(
                        text = stringResource(R.string.ram_usage_percentage, ramUsageInfo.usagePercentage.toInt()),
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = colorResource(R.color.text_black)
                    )
                }
            }



            Spacer(modifier = Modifier.width(16.dp))

            // RAM usage info
            Column {
                if (ramUsageInfo != null) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Box(modifier = Modifier
                            .width(2.dp)
                            .height(16.dp)
                            .background(colorResource(R.color.text_black),RoundedCornerShape(2.dp))
                        )

                        Spacer(modifier = Modifier.width(6.dp))

                        Text(
                            text = ramUsageInfo.usedMemoryFormatted,
                            fontSize = 18.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = colorResource(R.color.text_black)
                        )
                        Spacer(modifier = Modifier.width(4.dp))

                        Text(
                            text = stringResource(R.string.ram_used),
                            fontSize = 14.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = Color(0xFFA3A3A3)
                        )
                    }

                    Spacer(modifier = Modifier.height(4.dp))

                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Box(modifier = Modifier
                            .width(2.dp)
                            .height(16.dp)
                            .background(colorResource(R.color.text_black),RoundedCornerShape(2.dp))
                        )

                        Spacer(modifier = Modifier.width(6.dp))

                        Text(
                            text = ramUsageInfo.totalMemoryFormatted,
                            fontSize = 18.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = colorResource(R.color.text_black)
                        )
                        Spacer(modifier = Modifier.width(4.dp))

                        Text(
                            text = stringResource(R.string.ram_total),
                            fontSize = 14.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = Color(0xFFA3A3A3)
                        )
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    CustomProgress(ramUsageInfo.usagePercentage,
                        height = 20.dp,
                        width = 140.dp,
                        backgroundColor = Color(0xFFD8D8D8),
                        progressColor = Color(0xFF3D8FFF))
                } else {
                    Text(
                        text = stringResource(R.string.loading),
                        fontSize = 16.sp,
                        color = colorResource(R.color.text_black)
                    )
                }
            }
        }
}

/**
 * Feature suggestions section
 */
@Composable
fun FeatureSuggestionsSection() {
    Column {
        Text(
            text = stringResource(R.string.here_are_some_other_features),
            fontSize = 16.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        // Feature suggestion cards
        FeatureSuggestionCard(
            icon = Icons.Default.Storage,
            title = stringResource(R.string.device_storage),
            onClick = { /* TODO: Navigate to device storage */ }
        )

        Spacer(modifier = Modifier.height(12.dp))

        FeatureSuggestionCard(
            icon = Icons.Default.Security,
            title = stringResource(R.string.security_scan),
            onClick = { /* TODO: Navigate to security scan */ }
        )

        Spacer(modifier = Modifier.height(12.dp))

        FeatureSuggestionCard(
            icon = Icons.Default.BatteryFull,
            title = stringResource(R.string.battery_info),
            onClick = { /* TODO: Navigate to battery info */ }
        )
    }
}

/**
 * Feature suggestion card
 */
@Composable
fun FeatureSuggestionCard(
    icon: ImageVector,
    title: String,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Feature icon
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .background(
                        color = MaterialTheme.colorScheme.surfaceVariant,
                        shape = RoundedCornerShape(8.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = title,
                    modifier = Modifier.size(24.dp),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // Feature title
            Text(
                text = title,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.weight(1f)
            )

            // Arrow icon
            Icon(
                imageVector = Icons.Default.ArrowForward,
                contentDescription = null,
                modifier = Modifier.size(20.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * Battery info header showing current battery status
 */
@Composable
fun BatteryInfoHeader(batteryInfo: BatteryInfoUtils.BatteryInfo?) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Battery icon placeholder
            Box(
                modifier = Modifier
                    .size(80.dp)
                    .background(
                        color = MaterialTheme.colorScheme.surfaceVariant,
                        shape = RoundedCornerShape(8.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.BatteryFull,
                    contentDescription = stringResource(R.string.battery_image_placeholder),
                    modifier = Modifier.size(40.dp),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // Battery status info
            Column {
                if (batteryInfo != null) {
                    Text(
                        text = stringResource(batteryInfo.getHealthStringRes()),
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = stringResource(batteryInfo.getStatusStringRes()),
                        fontSize = 16.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                } else {
                    Text(
                        text = stringResource(R.string.loading),
                        fontSize = 16.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            Spacer(modifier = Modifier.weight(1f))

            // Battery percentage
            if (batteryInfo != null) {
                Text(
                    text = stringResource(R.string.battery_percentage, batteryInfo.level),
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }
    }
}

/**
 * Battery feature suggestions section
 */
@Composable
fun BatteryFeatureSuggestionsSection() {
    Column {
        Text(
            text = stringResource(R.string.here_are_some_other_features),
            fontSize = 16.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        // Feature suggestion cards
        FeatureSuggestionCard(
            icon = Icons.Default.Storage,
            title = stringResource(R.string.device_storage),
            onClick = { /* TODO: Navigate to device storage */ }
        )

        Spacer(modifier = Modifier.height(12.dp))

        FeatureSuggestionCard(
            icon = Icons.Default.Security,
            title = stringResource(R.string.security_scan),
            onClick = { /* TODO: Navigate to security scan */ }
        )

        Spacer(modifier = Modifier.height(12.dp))

        FeatureSuggestionCard(
            icon = Icons.Default.Memory,
            title = stringResource(R.string.ram_usage),
            onClick = { /* TODO: Navigate to RAM usage */ }
        )
    }
}

/**
 * CPU cores header showing total core count
 */
@Composable
fun CpuCoresHeader(cores: List<CpuInfoUtils.CpuCoreInfo>) {
    Text(
        text = stringResource(R.string.cpu_cores_count, cores.size),
        fontSize = 18.sp,
        fontWeight = FontWeight.Bold,
        color = MaterialTheme.colorScheme.onSurface,
        modifier = Modifier.padding(vertical = 8.dp)
    )
}

/**
 * CPU cores grid showing individual core frequencies
 */
@Composable
fun CpuCoresGrid(cores: List<CpuInfoUtils.CpuCoreInfo>) {
    LazyVerticalGrid(
        columns = GridCells.Fixed(2),
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp),
        modifier = Modifier.height((cores.size / 2 + cores.size % 2) * 80.dp)
    ) {
        items(cores) { core ->
            CpuCoreCard(core)
        }
    }
}

/**
 * Individual CPU core card
 */
@Composable
fun CpuCoreCard(core: CpuInfoUtils.CpuCoreInfo) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(70.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = stringResource(R.string.cpu_core_name, core.coreIndex),
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = if (core.currentFrequencyMHz >= 1000) {
                    stringResource(R.string.cpu_frequency_ghz, core.currentFrequencyGHz)
                } else {
                    stringResource(R.string.cpu_frequency_mhz, core.currentFrequencyMHz)
                },
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
        }
    }
}

/**
 * GLSurfaceView Composable for GPU info retrieval
 */
@Composable
fun GLSurfaceViewComposable(viewModel: CpuInfoViewModel) {
    AndroidView(
        factory = { context ->
            GLSurfaceView(context).apply {
                setEGLContextClientVersion(2)
                setRenderer(GpuInfoRenderer(viewModel))
                renderMode = GLSurfaceView.RENDERMODE_WHEN_DIRTY
            }
        },
        modifier = Modifier.size(1.dp)
    )
}

/**
 * GPU info renderer for GLSurfaceView
 */
class GpuInfoRenderer(private val viewModel: CpuInfoViewModel) : GLSurfaceView.Renderer {
    override fun onDrawFrame(gl: GL10?) {
        // Not needed for info retrieval
    }

    override fun onSurfaceCreated(gl: GL10?, config: EGLConfig?) {
        try {
            val version = GLES20.glGetString(GLES20.GL_VERSION) ?: "Unknown"
            val renderer = GLES20.glGetString(GLES20.GL_RENDERER) ?: "Unknown"
            val vendor = GLES20.glGetString(GLES20.GL_VENDOR) ?: "Unknown"

            logD("GpuInfo", "GPU Info Retrieved - Renderer: $renderer, Vendor: $vendor, Version: $version")

            viewModel._gpuRenderer.update { renderer }
            viewModel._gpuVersion.update { version }
            viewModel._gpuVendor.update { vendor }
        } catch (e: Exception) {
            logE("Error retrieving GPU info", e)
        }
    }

    override fun onSurfaceChanged(gl: GL10?, width: Int, height: Int) {
        // Not needed for info retrieval
    }
}

/**
 * App process content with running apps monitoring
 */
@Composable
fun AppProcessContent() {
    val context = LocalContext.current
    val viewModel: AppProcessViewModel = viewModel()
    val appProcessSummary by viewModel.appProcessSummary.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val stoppedApps by viewModel.stoppedApps.collectAsState()

    // Track lifecycle to check app status when returning from settings
    val lifecycleOwner = LocalLifecycleOwner.current

    LaunchedEffect(Unit) {
        viewModel.startMonitoring(context)
    }

    // Listen for lifecycle changes to check app status when returning
    LaunchedEffect(lifecycleOwner) {
        val observer = object : DefaultLifecycleObserver {
            override fun onResume(owner: LifecycleOwner) {
                // Check status of all apps that were marked as potentially stopped
                stoppedApps.forEach { packageName ->
                    viewModel.checkAppStatus(context, packageName)
                }
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
    }

    DisposableEffect(Unit) {
        onDispose {
            viewModel.stopMonitoring()
        }
    }

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // App process header
        item {
            AppProcessHeader(appProcessSummary)
        }

        // Non-stopped apps label
        if (appProcessSummary?.runningApps?.isNotEmpty() == true) {
            item {
                Text(
                    text = stringResource(R.string.non_stopped_apps),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }
        }

        if (appProcessSummary?.runningApps?.isNotEmpty() == true) {
            items(appProcessSummary!!.runningApps) { app ->
                AppProcessItem(
                    app = app,
                    isAppStopped = stoppedApps.contains(app.packageName),
                    onStopClick = { viewModel.stopApp(context, app.packageName) },
                    onAppClick = {  }
                )
            }
        } else if (!isLoading) {
            item {
                EmptyAppProcessState()
            }
        }

        if (isLoading) {
            item {
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
        }
    }
}

/**
 * App process header showing running apps count and RAM usage
 */
@Composable
fun AppProcessHeader(appProcessSummary: AppProcessUtils.AppProcessSummary?) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // App process icon placeholder
            Box(
                modifier = Modifier
                    .size(80.dp)
                    .background(
                        color = MaterialTheme.colorScheme.surfaceVariant,
                        shape = RoundedCornerShape(8.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Settings,
                    contentDescription = stringResource(R.string.app_process_monitor),
                    modifier = Modifier.size(40.dp),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // App process info
            Column {
                if (appProcessSummary != null) {
                    Text(
                        text = stringResource(R.string.running_apps_count, appProcessSummary.runningAppsCount),
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = stringResource(R.string.ram_usage_percentage, appProcessSummary.ramUsagePercentage.toInt()),
                        fontSize = 16.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                } else {
                    Text(
                        text = stringResource(R.string.loading),
                        fontSize = 16.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

/**
 * Individual app process item
 */
@Composable
fun AppProcessItem(
    app: AppProcessUtils.RunningAppInfo,
    isAppStopped: Boolean,
    onStopClick: () -> Unit,
    onAppClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onAppClick() },
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // App icon
            if (app.icon != null) {
                Image(
                    painter = BitmapPainter(app.icon.toBitmap().asImageBitmap()),
                    contentDescription = app.appName,
                    modifier = Modifier.size(48.dp)
                )
            } else {
                Box(
                    modifier = Modifier
                        .size(48.dp)
                        .background(
                            color = MaterialTheme.colorScheme.surfaceVariant,
                            shape = RoundedCornerShape(8.dp)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Android,
                        contentDescription = app.appName,
                        modifier = Modifier.size(24.dp),
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            Spacer(modifier = Modifier.width(6.dp))

            // App info
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = app.appName,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = app.packageName,
                    fontSize = 12.sp,
                    lineHeight = 12.sp,
                    maxLines = 2,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Spacer(modifier = Modifier.width(6.dp))


            // Stop button
            Button(
                onClick = onStopClick,
                enabled = !isAppStopped,
                modifier = Modifier.height(36.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = if (isAppStopped)
                        MaterialTheme.colorScheme.surfaceVariant
                    else
                        MaterialTheme.colorScheme.primary,
                    contentColor = if (isAppStopped)
                        MaterialTheme.colorScheme.onSurfaceVariant
                    else
                        MaterialTheme.colorScheme.onPrimary
                )
            ) {
                Text(
                    text = if (isAppStopped)
                        stringResource(R.string.stopped)
                    else
                        stringResource(R.string.stop),
                    fontSize = 12.sp
                )
            }
        }
    }
}

/**
 * Empty state for app process list
 */
@Composable
fun EmptyAppProcessState() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = Icons.Default.Settings,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = stringResource(R.string.no_running_apps),
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurface,
            textAlign = TextAlign.Center
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = stringResource(R.string.no_running_apps_description),
            fontSize = 14.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center
        )
    }
}