package com.example.clean0522.utils

import android.app.ActivityManager
import android.content.Context
import android.text.format.Formatter
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.delay
import java.io.BufferedReader
import java.io.FileReader

/**
 * Utility class for RAM usage monitoring
 */
object RamUsageUtils {

    /**
     * Data class for RAM usage information
     */
    data class RamUsageInfo(
        val usedMemoryBytes: Long,
        val totalMemoryBytes: Long,
        val availableMemoryBytes: Long,
        val usagePercentage: Float,
        val timestamp: Long = System.currentTimeMillis()
    ) {
        val usedMemoryFormatted: String
            get() = formatBytes(usedMemoryBytes)

        val totalMemoryFormatted: String
            get() = formatBytes(totalMemoryBytes)

        val availableMemoryFormatted: String
            get() = formatBytes(availableMemoryBytes)
    }

    /**
     * Get current RAM usage information
     */
    fun getCurrentRamUsage(context: Context): RamUsageInfo {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val memoryInfo = ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memoryInfo)

            val totalMemory = getTotalMemoryFromProcMeminfo()
            val availableMemory = memoryInfo.availMem
            val usedMemory = totalMemory - availableMemory
            val usagePercentage = if (totalMemory > 0) {
                (usedMemory.toFloat() / totalMemory.toFloat()) * 100f
            } else {
                0f
            }

            RamUsageInfo(
                usedMemoryBytes = usedMemory,
                totalMemoryBytes = totalMemory,
                availableMemoryBytes = availableMemory,
                usagePercentage = usagePercentage
            )
        } catch (e: Exception) {
            // Fallback to default values
            RamUsageInfo(
                usedMemoryBytes = 0L,
                totalMemoryBytes = 1L,
                availableMemoryBytes = 1L,
                usagePercentage = 0f
            )
        }
    }

    /**
     * Get real-time RAM usage flow that updates every second
     */
    fun getRamUsageFlow(context: Context): Flow<RamUsageInfo> = flow {
        while (true) {
            emit(getCurrentRamUsage(context))
            delay(1000) // Update every 1 second
        }
    }

    /**
     * Get total memory from /proc/meminfo for more accurate reading
     */
    private fun getTotalMemoryFromProcMeminfo(): Long {
        return try {
            val reader = BufferedReader(FileReader("/proc/meminfo"))
            val line = reader.readLine()
            reader.close()

            if (line != null) {
                val memInfo = line.split("\\s+".toRegex())
                if (memInfo.size >= 2) {
                    val totalKb = memInfo[1].toLong()
                    return totalKb * 1024 // Convert KB to bytes
                }
            }
            // Fallback to Runtime if /proc/meminfo fails
            Runtime.getRuntime().maxMemory()
        } catch (e: Exception) {
            // Fallback to Runtime
            Runtime.getRuntime().maxMemory()
        }
    }

    /**
     * Format bytes to human readable format
     */
    private fun formatBytes(bytes: Long): String {
        return when {
            bytes >= 1024 * 1024 * 1024 -> String.format("%.2f GB", bytes / (1024.0 * 1024.0 * 1024.0))
            bytes >= 1024 * 1024 -> String.format("%.2f MB", bytes / (1024.0 * 1024.0))
            bytes >= 1024 -> String.format("%.2f KB", bytes / 1024.0)
            else -> "$bytes B"
        }
    }

    /**
     * Data class for chart data point
     */
    data class ChartDataPoint(
        val percentage: Float,
        val timestamp: Long
    )

    /**
     * Manage chart data points (keep only last 10 points)
     */
    class ChartDataManager {
        private val maxDataPoints = 10
        private val dataPoints = mutableListOf<ChartDataPoint>()

        fun addDataPoint(ramUsageInfo: RamUsageInfo) {
            val newPoint = ChartDataPoint(
                percentage = ramUsageInfo.usagePercentage,
                timestamp = ramUsageInfo.timestamp
            )

            dataPoints.add(newPoint)

            // Keep only the last 10 points
            if (dataPoints.size > maxDataPoints) {
                dataPoints.removeAt(0)
            }
        }

        fun getDataPoints(): List<ChartDataPoint> = dataPoints.toList()

        fun getMinMaxPercentage(): Pair<Float, Float> {
            if (dataPoints.isEmpty()) return Pair(0f, 100f)

            val min = dataPoints.minOf { it.percentage }
            val max = dataPoints.maxOf { it.percentage }

            // If we only have one data point or all points are the same
            if (dataPoints.size == 1 || min == max) {
                val center = min
                val range = 20f // Show a 20% range around the single value
                return Pair(
                    (center - range / 2).coerceAtLeast(0f),
                    (center + range / 2).coerceAtMost(100f)
                )
            }

            // Add some padding to make changes more visible
            val padding = (max - min) * 0.2f // Increased padding for better visibility
            val adjustedMin = (min - padding).coerceAtLeast(0f)
            val adjustedMax = (max + padding).coerceAtMost(100f)

            // Ensure minimum range for visibility
            val minRange = 15f // Increased minimum range
            return if (adjustedMax - adjustedMin < minRange) {
                val center = (adjustedMin + adjustedMax) / 2f
                val halfRange = minRange / 2f
                Pair(
                    (center - halfRange).coerceAtLeast(0f),
                    (center + halfRange).coerceAtMost(100f)
                )
            } else {
                Pair(adjustedMin, adjustedMax)
            }
        }
    }
}
