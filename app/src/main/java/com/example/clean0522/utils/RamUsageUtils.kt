package com.example.clean0522.utils

import android.app.ActivityManager
import android.content.Context
import android.text.format.Formatter
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.delay
import java.io.BufferedReader
import java.io.FileReader

/**
 * Utility class for RAM usage monitoring
 */
object RamUsageUtils {

    /**
     * Data class for RAM usage information
     */
    data class RamUsageInfo(
        val usedMemoryBytes: Long,
        val totalMemoryBytes: Long,
        val availableMemoryBytes: Long,
        val usagePercentage: Float,
        val timestamp: Long = System.currentTimeMillis()
    ) {
        val usedMemoryFormatted: String
            get() = formatBytes(usedMemoryBytes)

        val totalMemoryFormatted: String
            get() = formatBytes(totalMemoryBytes)

        val availableMemoryFormatted: String
            get() = formatBytes(availableMemoryBytes)
    }

    /**
     * Get current RAM usage information
     */
    fun getCurrentRamUsage(context: Context): RamUsageInfo {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val memoryInfo = ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memoryInfo)

            val totalMemory = getTotalMemoryFromProcMeminfo()
            val availableMemory = memoryInfo.availMem
            val usedMemory = totalMemory - availableMemory
            val usagePercentage = if (totalMemory > 0) {
                (usedMemory.toFloat() / totalMemory.toFloat()) * 100f
            } else {
                0f
            }

            RamUsageInfo(
                usedMemoryBytes = usedMemory,
                totalMemoryBytes = totalMemory,
                availableMemoryBytes = availableMemory,
                usagePercentage = usagePercentage
            )
        } catch (e: Exception) {
            // Fallback to default values
            RamUsageInfo(
                usedMemoryBytes = 0L,
                totalMemoryBytes = 1L,
                availableMemoryBytes = 1L,
                usagePercentage = 0f
            )
        }
    }

    /**
     * Get real-time RAM usage flow that updates every second
     */
    fun getRamUsageFlow(context: Context): Flow<RamUsageInfo> = flow {
        while (true) {
            emit(getCurrentRamUsage(context))
            delay(1000) // Update every 1 second
        }
    }

    /**
     * Get total memory from /proc/meminfo for more accurate reading
     */
    private fun getTotalMemoryFromProcMeminfo(): Long {
        return try {
            val reader = BufferedReader(FileReader("/proc/meminfo"))
            val line = reader.readLine()
            reader.close()

            if (line != null) {
                val memInfo = line.split("\\s+".toRegex())
                if (memInfo.size >= 2) {
                    val totalKb = memInfo[1].toLong()
                    return totalKb * 1024 // Convert KB to bytes
                }
            }
            // Fallback to Runtime if /proc/meminfo fails
            Runtime.getRuntime().maxMemory()
        } catch (e: Exception) {
            // Fallback to Runtime
            Runtime.getRuntime().maxMemory()
        }
    }

    /**
     * Format bytes to human readable format
     */
    private fun formatBytes(bytes: Long): String {
        return when {
            bytes >= 1024 * 1024 * 1024 -> String.format("%.2f GB", bytes / (1024.0 * 1024.0 * 1024.0))
            bytes >= 1024 * 1024 -> String.format("%.2f MB", bytes / (1024.0 * 1024.0))
            bytes >= 1024 -> String.format("%.2f KB", bytes / 1024.0)
            else -> "$bytes B"
        }
    }

    /**
     * Data class for chart data point
     */
    data class ChartDataPoint(
        val usedMemoryBytes: Long,
        val timestamp: Long
    )

    /**
     * Manage chart data points (keep only last 10 points) with smooth transitions
     */
    class ChartDataManager {
        private val maxDataPoints = 10
        private val dataPoints = mutableListOf<ChartDataPoint>()

        fun addDataPoint(ramUsageInfo: RamUsageInfo) {
            val newPoint = ChartDataPoint(
                usedMemoryBytes = ramUsageInfo.usedMemoryBytes,
                timestamp = ramUsageInfo.timestamp
            )

            dataPoints.add(newPoint)

            // Keep only the last 10 points
            if (dataPoints.size > maxDataPoints) {
                dataPoints.removeAt(0)
            }
        }

        fun getDataPoints(): List<ChartDataPoint> = dataPoints.toList()

        /**
         * Get interpolated data points for smooth transitions
         * This creates a smooth progression from empty to full chart
         */
        fun getInterpolatedDataPoints(): List<ChartDataPoint> {
            if (dataPoints.isEmpty()) return emptyList()

            // If we have fewer than maxDataPoints, create interpolated points for smooth transition
            return when {
                dataPoints.size == 1 -> {
                    // For single point, create a gentle curve around it
                    val point = dataPoints[0]
                    val baseMemory = point.usedMemoryBytes
                    val variation = 15L * 1024L * 1024L // 15MB variation

                    listOf(
                        ChartDataPoint(baseMemory - variation, point.timestamp - 3000),
                        ChartDataPoint(baseMemory - variation/2, point.timestamp - 2000),
                        ChartDataPoint(baseMemory, point.timestamp - 1000),
                        ChartDataPoint(baseMemory + variation/3, point.timestamp),
                        ChartDataPoint(baseMemory + variation/2, point.timestamp + 1000)
                    )
                }
                dataPoints.size < maxDataPoints -> {
                    // Interpolate to create smooth progression
                    interpolateToTargetSize(dataPoints, maxDataPoints)
                }
                else -> dataPoints.toList()
            }
        }

        /**
         * Interpolate data points to create smooth transitions
         */
        private fun interpolateToTargetSize(points: List<ChartDataPoint>, targetSize: Int): List<ChartDataPoint> {
            if (points.size >= targetSize) return points

            val result = mutableListOf<ChartDataPoint>()
            val timeStep = 1000L // 1 second between interpolated points

            // Add existing points
            result.addAll(points)

            // Calculate how many points we need to add
            val pointsToAdd = targetSize - points.size

            if (points.size >= 2) {
                // Use the trend from the last two points to extrapolate
                val lastPoint = points.last()
                val secondLastPoint = points[points.size - 2]

                val memoryTrend = (lastPoint.usedMemoryBytes - secondLastPoint.usedMemoryBytes).toFloat() /
                                 (lastPoint.timestamp - secondLastPoint.timestamp).toFloat()

                // Add interpolated points with some randomness for natural variation
                for (i in 1..pointsToAdd) {
                    val timeOffset = timeStep * i
                    val trendMemory = (memoryTrend * timeOffset).toLong()

                    // Add small random variation (±10MB) for natural look
                    val randomVariation = ((-10..10).random() * 1024L * 1024L)
                    val interpolatedMemory = lastPoint.usedMemoryBytes + trendMemory + randomVariation

                    result.add(ChartDataPoint(
                        usedMemoryBytes = interpolatedMemory.coerceAtLeast(0L),
                        timestamp = lastPoint.timestamp + timeOffset
                    ))
                }
            } else if (points.size == 1) {
                // For single point, create gentle variations
                val basePoint = points[0]
                val baseMemory = basePoint.usedMemoryBytes

                for (i in 1..pointsToAdd) {
                    val variation = ((-20..20).random() * 1024L * 1024L) // ±20MB variation
                    result.add(ChartDataPoint(
                        usedMemoryBytes = (baseMemory + variation).coerceAtLeast(0L),
                        timestamp = basePoint.timestamp + timeStep * i
                    ))
                }
            }

            return result.takeLast(targetSize)
        }

        fun getMinMaxMemoryBytes(): Pair<Long, Long> {
            if (dataPoints.isEmpty()) return Pair(0L, 1024L * 1024L * 1024L) // Default 0 to 1GB

            val min = dataPoints.minOf { it.usedMemoryBytes }
            val max = dataPoints.maxOf { it.usedMemoryBytes }

            // If we only have one data point or all points are the same
            if (dataPoints.size == 1 || min == max) {
                val center = min
                val range = 100L * 1024L * 1024L // Show a 100MB range around the single value for sensitivity
                return Pair(
                    (center - range / 2).coerceAtLeast(0L),
                    center + range / 2
                )
            }

            // Add padding to make changes more visible - use smaller padding for more sensitivity
            val padding = ((max - min) * 0.1f).toLong() // Reduced padding for higher sensitivity
            val adjustedMin = (min - padding).coerceAtLeast(0L)
            val adjustedMax = max + padding

            // Ensure minimum range for visibility - smaller range for more sensitivity
            val minRange = 50L * 1024L * 1024L // 50MB minimum range for high sensitivity
            return if (adjustedMax - adjustedMin < minRange) {
                val center = (adjustedMin + adjustedMax) / 2L
                val halfRange = minRange / 2L
                Pair(
                    (center - halfRange).coerceAtLeast(0L),
                    center + halfRange
                )
            } else {
                Pair(adjustedMin, adjustedMax)
            }
        }
    }
}
